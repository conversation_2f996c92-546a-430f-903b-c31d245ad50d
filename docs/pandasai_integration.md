# PandasAI 数据分析功能集成文档

## 概述

本文档描述了在 mcp_server_cms 中集成的 PandasAI 数据分析功能，该功能允许用户使用自然语言对业务数据进行智能分析。

## 功能特性

### 🤖 AI 驱动的数据分析
- **自然语言查询**: 支持中文和英文的自然语言问题
- **智能洞察**: 自动识别数据趋势、异常和关键指标
- **多维分析**: 支持时间序列、客户、利润等多角度分析
- **实时处理**: 基于最新业务数据进行即时分析

### 📊 支持的数据类型
- **Booking数据**: 订舱毛利数据，包含转运业务利润
- **Job数据**: 工作档明细数据，包含统计信息

### 🔒 安全特性
- **隐私保护**: 启用 PandasAI 的隐私保护模式
- **访问控制**: 需要有效的 MCP Token
- **超时保护**: 防止长时间运行的查询

### ⚡ 性能优化
- **智能缓存**: 自动缓存数据查询结果，避免重复数据库查询
- **跨端点缓存共享**: 导出和分析功能共享缓存，提升整体性能
- **中文列名缓存**: AI分析专用缓存，存储中文列名格式数据，提升分析质量
- **缓存管理**: 支持缓存统计、清理和配置
- **连续查询优化**: 同一时间段的连续分析问题无需重新获取数据

## API 接口

### 数据分析端点

**URL**: `POST /analyze/data`

**请求参数**:
```json
{
    "begin_date": "2024-01-01",
    "end_date": "2024-01-31", 
    "data_type": "booking",
    "question": "分析最近一个月的订舱数据，包括总收入、总成本、利润率",
    "language": "zh"
}
```

**参数说明**:
- `begin_date`: 开始日期 (YYYY-MM-DD 格式)
- `end_date`: 结束日期 (YYYY-MM-DD 格式)
- `data_type`: 数据类型，支持 "booking" 或 "job"
- `question`: 分析问题（自然语言）
- `language`: 返回语言，支持 "zh"（中文）或 "en"（英文）

**响应示例**:
```json
{
    "success": true,
    "message": "数据分析完成",
    "analysis_result": "根据分析结果，最近一个月的订舱数据显示...",
    "question": "分析最近一个月的订舱数据，包括总收入、总成本、利润率",
    "language": "zh",
    "data_info": {
        "data_type": "booking",
        "date_range": "2024-01-01 至 2024-01-31",
        "record_count": 1250
    },
    "performance_info": {
        "total_execution_time_seconds": 15.67,
        "data_fetch_time_seconds": 0.0,
        "cache_used": true,
        "cache_stats": {
            "cache_size": 3,
            "max_cache_size": 50,
            "hit_rate": 66.7,
            "total_hits": 2,
            "total_misses": 1,
            "total_requests": 3,
            "expire_minutes": 30
        }
    },
    "timestamp": "2024-01-31T10:30:00"
}
```

### 缓存管理端点

#### 获取缓存统计

**URL**: `GET /cache/stats`

**响应示例**:
```json
{
    "success": true,
    "message": "缓存统计信息获取成功",
    "cache_stats": {
        "cache_size": 5,
        "max_cache_size": 50,
        "hit_rate": 80.0,
        "total_hits": 8,
        "total_misses": 2,
        "total_requests": 10,
        "expire_minutes": 30
    },
    "timestamp": "2024-01-31T10:30:00"
}
```

#### 清空缓存

**URL**: `POST /cache/clear`

**响应示例**:
```json
{
    "success": true,
    "message": "缓存已清空",
    "cleared_items": 5,
    "timestamp": "2024-01-31T10:30:00"
}
```

## 使用示例

### 1. 订舱数据分析示例

```python
import requests

# 分析订舱数据的利润趋势
request_data = {
    "begin_date": "2024-01-01",
    "end_date": "2024-01-31",
    "data_type": "booking",
    "question": "分析各个业务类型的利润率，找出最盈利的业务类型",
    "language": "zh"
}

response = requests.post(
    "http://127.0.0.1:8011/analyze/data",
    headers={"Authorization": "Bearer YOUR_MCP_TOKEN"},
    json=request_data
)
```

### 2. 工作档数据分析示例

```python
# 分析工作档的操作效率
request_data = {
    "begin_date": "2024-01-01", 
    "end_date": "2024-01-31",
    "data_type": "job",
    "question": "分析操作完成率和审核通过率，找出效率最高的操作员",
    "language": "zh"
}
```

### 3. 常见分析问题示例

**财务分析**:
- "分析最近三个月的收入趋势"
- "找出利润率最高的客户"
- "计算各业务类型的平均利润率"

**运营分析**:
- "分析操作完成情况和延误原因"
- "统计各港口的业务量分布"
- "分析转运业务的占比和效益"

**客户分析**:
- "找出最活跃的客户和代理"
- "分析客户的业务类型偏好"
- "计算客户的平均订单价值"

## 配置要求

### 环境变量
确保以下环境变量已正确配置：

```bash
# Azure OpenAI 配置
AZURE_OPENAI_API_KEY=your_api_key
AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com/
AZURE_API_VERSION=2024-02-15-preview
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4.1-mini

# MCP Token
MCP_TOKEN=your_mcp_token

# 缓存配置（可选）
DATA_CACHE_EXPIRE_MINUTES=30    # 缓存过期时间（分钟），默认30分钟
DATA_CACHE_MAX_SIZE=50          # 最大缓存条目数，默认50个
```

### 依赖包
- `pandasai>=2.0.24`
- `pandas>=2.3.1`
- `fastapi`
- `pydantic`

## 性能优化

### 超时设置
- **数据获取超时**: 5分钟
- **AI分析超时**: 3分钟
- **总体请求超时**: 建议设置为10分钟

### 数据量建议
- **小数据集** (< 1000条): 响应时间通常在10-30秒
- **中等数据集** (1000-5000条): 响应时间通常在30-60秒
- **大数据集** (> 5000条): 建议缩小日期范围或使用更具体的查询

## 错误处理

### 常见错误类型
1. **数据为空**: 指定日期范围内没有数据
2. **API配置错误**: Azure OpenAI 配置不正确
3. **分析超时**: 查询过于复杂或数据量过大
4. **权限错误**: MCP Token 无效或过期

### 错误响应示例
```json
{
    "success": false,
    "message": "指定日期范围内没有找到booking数据",
    "analysis_result": "没有可分析的数据。请检查日期范围。",
    "data_info": {
        "data_type": "booking",
        "date_range": "2024-01-01 至 2024-01-31",
        "record_count": 0
    }
}
```

## 最佳实践

### 1. 问题设计
- 使用具体、明确的问题
- 避免过于复杂的多重查询
- 指定需要的分析维度

### 2. 日期范围
- 根据数据量调整日期范围
- 对于趋势分析，建议使用较长时间段
- 对于详细分析，建议使用较短时间段

### 3. 语言选择
- 中文问题使用 `language: "zh"`
- 英文问题使用 `language: "en"`
- 保持问题语言与返回语言一致

### 4. 缓存优化
- **连续查询**: 对同一时间段进行多次分析时，第二次及后续查询将使用缓存
- **跨端点共享**: 先导出数据再分析时，分析操作将直接使用导出时的缓存
- **缓存监控**: 定期检查缓存命中率，优化查询模式
- **缓存清理**: 在数据更新后可手动清空缓存以获取最新数据
- **内存管理**: 系统自动管理缓存大小，无需手动干预

## 跨端点缓存共享示例

### 场景：先导出后分析
```python
# 步骤1: 导出2024年5月的job数据
export_request = {
    "begin_date": "2024-05-01",
    "end_date": "2024-05-31",
    "format": "excel"
}
response1 = requests.post("/export/jobs", json=export_request)
# 第一次查询：从数据库获取数据，耗时45秒，数据被缓存

# 步骤2: 分析相同时间段的job数据
analysis_request = {
    "begin_date": "2024-05-01",
    "end_date": "2024-05-31",
    "data_type": "job",
    "question": "分析操作完成情况和效率",
    "language": "zh"
}
response2 = requests.post("/analyze/data", json=analysis_request)
# 第二次查询：使用缓存，数据获取耗时0秒，总耗时仅15秒
```

### 性能对比
- **无缓存**: 导出45秒 + 分析45秒 = 总计90秒
- **有缓存**: 导出45秒 + 分析15秒 = 总计60秒，节省33%时间

## 中文列名缓存优化

### 问题背景
原始数据库字段为英文名称（如 `business_type_name`, `job_file_no`），但AI分析时使用中文字段名（如 `业务类型`, `工作档编号`）能获得更好的分析效果。

### 解决方案
系统实现了专门的AI分析缓存层：

1. **数据转换**: 将英文字段名转换为中文字段名
2. **专用缓存**: 缓存转换后的中文列名数据
3. **智能复用**: AI分析直接使用中文格式缓存

### 缓存层级
```
原始数据缓存 (英文字段名)
    ↓ 字段名转换
AI分析缓存 (中文字段名) ← AI分析使用
```

### 字段映射示例
```python
# 原始数据字段
{
    "business_type_name": "海运出口",
    "job_file_no": "SE240501001",
    "income": 5000.0,
    "cost": 3000.0
}

# 转换后的AI分析数据
{
    "业务类型": "海运出口",
    "工作档编号": "SE240501001",
    "收入": 5000.0,
    "成本": 3000.0
}
```

### 性能优势
- **首次分析**: 获取数据 + 字段转换 + AI分析
- **后续分析**: 直接使用中文格式缓存 + AI分析
- **分析质量**: 中文字段名提升AI理解和分析准确性

## 集成到 MCP 客户端

该功能已自动集成到 MCP 服务中，可以通过任何支持 MCP 协议的客户端使用：

```python
# 通过 MCP 客户端调用
mcp_client.call_tool(
    "analyze_data",
    {
        "begin_date": "2024-01-01",
        "end_date": "2024-01-31", 
        "data_type": "booking",
        "question": "分析业务数据趋势",
        "language": "zh"
    }
)
```

## 技术架构

```
用户请求 → FastAPI端点 → 数据获取 → DataFrame转换 → PandasAI分析 → 结果返回
    ↓           ↓            ↓           ↓            ↓           ↓
MCP协议 → analyze_data → DB查询 → pandas.DataFrame → SmartDataframe → AI响应
```

## 支持与反馈

如有问题或建议，请通过以下方式联系：
- 查看日志文件获取详细错误信息
- 检查环境变量配置
- 验证数据库连接状态
- 测试 Azure OpenAI API 连接
