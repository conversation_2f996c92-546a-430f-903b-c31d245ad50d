2025-07-07 21:54:40 - __main__ - WARNING - 系统信息: macOS-15.5-arm64-arm-64bit, Python: 3.12.9
2025-07-07 21:54:40 - __main__ - WARNING - CPU核心数: 11, 配置的Worker数: 8
2025-07-07 21:54:40 - __main__ - WARNING - 主机名: lixiaojuns-MacBook-Pro.local, IP: 127.0.0.1
2025-07-07 21:54:40 - __main__ - WARNING - 服务监听: 0.0.0.0:8011, 基础URL: http://127.0.0.1:8011
2025-07-07 21:54:40 - __main__ - WARNING - 检测到本地开发环境，使用标准配置
2025-07-07 21:54:40 - __main__ - WARNING - 已完成优化版MCP服务挂载:
2025-07-07 21:54:40 - __main__ - WARNING -   - /mcp: CMS企业管理系统 (全功能优化版)
2025-07-07 21:54:40 - __main__ - WARNING -     * MCP SSE端点: /mcp/sse
2025-07-07 21:54:40 - __main__ - WARNING -     * MCP Tools端点: /mcp/tools
2025-07-07 21:54:40 - __main__ - WARNING -     * MCP Resources端点: /mcp/resources
2025-07-07 21:54:40 - __main__ - WARNING -     * 主要功能: 搜索、数据导出、AI分析、系统监控
2025-07-07 21:54:40 - __main__ - WARNING - Prometheus监控服务启动在 :8111
2025-07-07 21:54:40 - __main__ - WARNING - === CMS企业管理系统MCP服务器启动信息 ===
2025-07-07 21:54:40 - __main__ - WARNING - 🚀 启动时间: 2025-07-07 21:54:40
2025-07-07 21:54:40 - __main__ - WARNING - 🌐 服务地址: http://0.0.0.0:8011
2025-07-07 21:54:40 - __main__ - WARNING - 🔥 精简版特色: 专注数据导出，高效可靠，支持名称查询
2025-07-07 21:54:40 - __main__ - WARNING - 📊 MCP连接信息:
2025-07-07 21:54:40 - __main__ - WARNING -    - SSE连接地址: http://0.0.0.0:8011/mcp/sse
2025-07-07 21:54:40 - __main__ - WARNING -    - Tools端点: http://0.0.0.0:8011/mcp/tools
2025-07-07 21:54:40 - __main__ - WARNING -    - Resources端点: http://0.0.0.0:8011/mcp/resources
2025-07-07 21:54:40 - __main__ - WARNING - 📊 主要功能模块:
2025-07-07 21:54:40 - __main__ - WARNING -    1. 🔍 名称查询 - 人员/公司/部门精确搜索
2025-07-07 21:54:40 - __main__ - WARNING -    2. 🗄️ 数据导出 - Job/Booking数据Excel/CSV导出
2025-07-07 21:54:40 - __main__ - WARNING -    3. 📊 系统监控 - 健康检查和状态监控
2025-07-07 21:54:40 - __main__ - WARNING - 🔒 安全特性:
2025-07-07 21:54:40 - __main__ - WARNING -    - 访问控制: 严格的权限验证和访问控制
2025-07-07 21:54:40 - __main__ - WARNING -    - 数据安全: 安全的数据查询和导出
2025-07-07 21:54:40 - __main__ - WARNING -    - 超时保护: 导出超时保护和错误处理
2025-07-07 21:54:40 - __main__ - WARNING -    - 认证: MCP Token验证已启用
2025-07-07 21:54:40 - __main__ - WARNING - ⚙️ 配置: 单worker模式, 专注数据导出, 并发限制: 100
2025-07-07 21:54:40 - __main__ - WARNING - 🔧 网络: 连接保持: 600s, 队列: 256
2025-07-07 21:54:40 - __main__ - WARNING - ============================================================
2025-07-07 21:54:40 - __main__ - WARNING - 正在初始化MCP服务组件...
2025-07-07 21:54:42 - __main__ - WARNING - MCP服务初始化完成，开始接受连接
2025-07-07 21:54:42 - __main__ - WARNING - ✅ 利润数据周期性调度器将在FastAPI应用启动时自动启动
2025-07-07 21:54:42 - __main__ - WARNING - 📋 调度功能说明：
2025-07-07 21:54:42 - __main__ - WARNING -   - 自动分析job_details和booking_details数据
2025-07-07 21:54:42 - __main__ - WARNING -   - 智能变更检测，只保存有变化的数据
2025-07-07 21:54:42 - __main__ - WARNING -   - 分层调度：越久远的数据检查频率越低
2025-07-07 21:54:42 - __main__ - WARNING -   - 执行窗口：周末07:00-24:00
2025-07-07 21:54:42 - __main__ - WARNING - 开始启动CMS企业管理系统服务器...
2025-07-07 22:00:14 - __main__ - WARNING - 收到信号 2，开始优雅关闭...
2025-07-07 22:29:12 - __main__ - WARNING - 系统信息: macOS-15.5-arm64-arm-64bit, Python: 3.12.9
2025-07-07 22:29:12 - __main__ - WARNING - CPU核心数: 11, 配置的Worker数: 8
2025-07-07 22:29:12 - __main__ - WARNING - 主机名: lixiaojuns-MacBook-Pro.local, IP: 127.0.0.1
2025-07-07 22:29:12 - __main__ - WARNING - 服务监听: 0.0.0.0:8011, 基础URL: http://127.0.0.1:8011
2025-07-07 22:29:12 - __main__ - WARNING - 检测到本地开发环境，使用标准配置
2025-07-07 22:29:12 - __main__ - WARNING - 已完成优化版MCP服务挂载:
2025-07-07 22:29:12 - __main__ - WARNING -   - /mcp: CMS企业管理系统 (全功能优化版)
2025-07-07 22:29:12 - __main__ - WARNING -     * MCP SSE端点: /mcp/sse
2025-07-07 22:29:12 - __main__ - WARNING -     * MCP Tools端点: /mcp/tools
2025-07-07 22:29:12 - __main__ - WARNING -     * MCP Resources端点: /mcp/resources
2025-07-07 22:29:12 - __main__ - WARNING -     * 主要功能: 搜索、数据导出、AI分析、系统监控
2025-07-07 22:29:12 - __main__ - WARNING - Prometheus监控服务启动在 :8111
2025-07-07 22:29:12 - __main__ - WARNING - === CMS企业管理系统MCP服务器启动信息 ===
2025-07-07 22:29:12 - __main__ - WARNING - 🚀 启动时间: 2025-07-07 22:29:12
2025-07-07 22:29:12 - __main__ - WARNING - 🌐 服务地址: http://0.0.0.0:8011
2025-07-07 22:29:12 - __main__ - WARNING - 🔥 精简版特色: 专注数据导出，高效可靠，支持名称查询
2025-07-07 22:29:12 - __main__ - WARNING - 📊 MCP连接信息:
2025-07-07 22:29:12 - __main__ - WARNING -    - SSE连接地址: http://0.0.0.0:8011/mcp/sse
2025-07-07 22:29:12 - __main__ - WARNING -    - Tools端点: http://0.0.0.0:8011/mcp/tools
2025-07-07 22:29:12 - __main__ - WARNING -    - Resources端点: http://0.0.0.0:8011/mcp/resources
2025-07-07 22:29:12 - __main__ - WARNING - 📊 主要功能模块:
2025-07-07 22:29:12 - __main__ - WARNING -    1. 🔍 名称查询 - 人员/公司/部门精确搜索
2025-07-07 22:29:12 - __main__ - WARNING -    2. 🗄️ 数据导出 - Job/Booking数据Excel/CSV导出
2025-07-07 22:29:12 - __main__ - WARNING -    3. 📊 系统监控 - 健康检查和状态监控
2025-07-07 22:29:12 - __main__ - WARNING - 🔒 安全特性:
2025-07-07 22:29:12 - __main__ - WARNING -    - 访问控制: 严格的权限验证和访问控制
2025-07-07 22:29:12 - __main__ - WARNING -    - 数据安全: 安全的数据查询和导出
2025-07-07 22:29:12 - __main__ - WARNING -    - 超时保护: 导出超时保护和错误处理
2025-07-07 22:29:12 - __main__ - WARNING -    - 认证: MCP Token验证已启用
2025-07-07 22:29:12 - __main__ - WARNING - ⚙️ 配置: 单worker模式, 专注数据导出, 并发限制: 100
2025-07-07 22:29:12 - __main__ - WARNING - 🔧 网络: 连接保持: 600s, 队列: 256
2025-07-07 22:29:12 - __main__ - WARNING - ============================================================
2025-07-07 22:29:12 - __main__ - WARNING - 正在初始化MCP服务组件...
2025-07-07 22:29:14 - __main__ - WARNING - MCP服务初始化完成，开始接受连接
2025-07-07 22:29:14 - __main__ - WARNING - ⏸️ 自动提取功能已禁用
2025-07-07 22:29:14 - __main__ - WARNING - 💡 如需启用往期job和booking业务数据自动提取功能，请使用 --enable-auto-extract 参数
2025-07-07 22:29:14 - __main__ - WARNING - 开始启动CMS企业管理系统服务器...
2025-07-07 22:40:06 - __main__ - WARNING - 收到信号 2，开始优雅关闭...
2025-07-07 22:40:40 - __main__ - WARNING - 系统信息: macOS-15.5-arm64-arm-64bit, Python: 3.12.9
2025-07-07 22:40:40 - __main__ - WARNING - CPU核心数: 11, 配置的Worker数: 8
2025-07-07 22:40:40 - __main__ - WARNING - 主机名: lixiaojuns-MacBook-Pro.local, IP: 127.0.0.1
2025-07-07 22:40:40 - __main__ - WARNING - 服务监听: 0.0.0.0:8011, 基础URL: http://127.0.0.1:8011
2025-07-07 22:40:40 - __main__ - WARNING - 检测到本地开发环境，使用标准配置
2025-07-07 22:40:40 - __main__ - WARNING - 已完成优化版MCP服务挂载:
2025-07-07 22:40:40 - __main__ - WARNING -   - /mcp: CMS企业管理系统 (全功能优化版)
2025-07-07 22:40:40 - __main__ - WARNING -     * MCP SSE端点: /mcp/sse
2025-07-07 22:40:40 - __main__ - WARNING -     * MCP Tools端点: /mcp/tools
2025-07-07 22:40:40 - __main__ - WARNING -     * MCP Resources端点: /mcp/resources
2025-07-07 22:40:40 - __main__ - WARNING -     * 主要功能: 搜索、数据导出、AI分析、系统监控
2025-07-07 22:40:40 - __main__ - WARNING - Prometheus监控服务启动在 :8111
2025-07-07 22:40:40 - __main__ - WARNING - === CMS企业管理系统MCP服务器启动信息 ===
2025-07-07 22:40:40 - __main__ - WARNING - 🚀 启动时间: 2025-07-07 22:40:40
2025-07-07 22:40:40 - __main__ - WARNING - 🌐 服务地址: http://0.0.0.0:8011
2025-07-07 22:40:40 - __main__ - WARNING - 🔥 精简版特色: 专注数据导出，高效可靠，支持名称查询
2025-07-07 22:40:40 - __main__ - WARNING - 📊 MCP连接信息:
2025-07-07 22:40:40 - __main__ - WARNING -    - SSE连接地址: http://0.0.0.0:8011/mcp/sse
2025-07-07 22:40:40 - __main__ - WARNING -    - Tools端点: http://0.0.0.0:8011/mcp/tools
2025-07-07 22:40:40 - __main__ - WARNING -    - Resources端点: http://0.0.0.0:8011/mcp/resources
2025-07-07 22:40:40 - __main__ - WARNING - 📊 主要功能模块:
2025-07-07 22:40:40 - __main__ - WARNING -    1. 🔍 名称查询 - 人员/公司/部门精确搜索
2025-07-07 22:40:40 - __main__ - WARNING -    2. 🗄️ 数据导出 - Job/Booking数据Excel/CSV导出
2025-07-07 22:40:40 - __main__ - WARNING -    3. 📊 系统监控 - 健康检查和状态监控
2025-07-07 22:40:40 - __main__ - WARNING - 🔒 安全特性:
2025-07-07 22:40:40 - __main__ - WARNING -    - 访问控制: 严格的权限验证和访问控制
2025-07-07 22:40:40 - __main__ - WARNING -    - 数据安全: 安全的数据查询和导出
2025-07-07 22:40:40 - __main__ - WARNING -    - 超时保护: 导出超时保护和错误处理
2025-07-07 22:40:40 - __main__ - WARNING -    - 认证: MCP Token验证已启用
2025-07-07 22:40:40 - __main__ - WARNING - ⚙️ 配置: 单worker模式, 专注数据导出, 并发限制: 100
2025-07-07 22:40:40 - __main__ - WARNING - 🔧 网络: 连接保持: 600s, 队列: 256
2025-07-07 22:40:40 - __main__ - WARNING - ============================================================
2025-07-07 22:40:40 - __main__ - WARNING - 正在初始化MCP服务组件...
2025-07-07 22:40:42 - __main__ - WARNING - MCP服务初始化完成，开始接受连接
2025-07-07 22:40:42 - __main__ - WARNING - ⏸️ 自动提取功能已禁用
2025-07-07 22:40:42 - __main__ - WARNING - 💡 如需启用往期job和booking业务数据自动提取功能，请使用 --enable-auto-extract 参数
2025-07-07 22:40:42 - __main__ - WARNING - 开始启动CMS企业管理系统服务器...
2025-07-07 22:52:56 - __main__ - WARNING - 收到信号 2，开始优雅关闭...
2025-07-08 23:11:54 - __main__ - WARNING - 系统信息: macOS-15.5-arm64-arm-64bit, Python: 3.12.9
2025-07-08 23:11:54 - __main__ - WARNING - CPU核心数: 11, 配置的Worker数: 8
2025-07-08 23:11:54 - __main__ - WARNING - 主机名: lixiaojuns-MacBook-Pro.local, IP: 127.0.0.1
2025-07-08 23:11:54 - __main__ - WARNING - 服务监听: 0.0.0.0:8011, 基础URL: http://127.0.0.1:8011
2025-07-08 23:11:54 - __main__ - WARNING - 检测到本地开发环境，使用标准配置
2025-07-08 23:11:54 - __main__ - WARNING - 已完成优化版MCP服务挂载:
2025-07-08 23:11:54 - __main__ - WARNING -   - /mcp: CMS企业管理系统 (全功能优化版)
2025-07-08 23:11:54 - __main__ - WARNING -     * MCP SSE端点: /mcp/sse
2025-07-08 23:11:54 - __main__ - WARNING -     * MCP Tools端点: /mcp/tools
2025-07-08 23:11:54 - __main__ - WARNING -     * MCP Resources端点: /mcp/resources
2025-07-08 23:11:54 - __main__ - WARNING -     * 主要功能: 搜索、数据导出、PandasAI分析、系统监控
2025-07-08 23:11:54 - __main__ - WARNING - Prometheus监控服务启动在 :8111
2025-07-08 23:11:54 - __main__ - WARNING - === CMS企业管理系统MCP服务器启动信息 ===
2025-07-08 23:11:54 - __main__ - WARNING - 🚀 启动时间: 2025-07-08 23:11:54
2025-07-08 23:11:54 - __main__ - WARNING - 🌐 服务地址: http://0.0.0.0:8011
2025-07-08 23:11:54 - __main__ - WARNING - 🔥 精简版特色: 专注数据导出，高效可靠，支持名称查询
2025-07-08 23:11:54 - __main__ - WARNING - 📊 MCP连接信息:
2025-07-08 23:11:54 - __main__ - WARNING -    - SSE连接地址: http://0.0.0.0:8011/mcp/sse
2025-07-08 23:11:54 - __main__ - WARNING -    - Tools端点: http://0.0.0.0:8011/mcp/tools
2025-07-08 23:11:54 - __main__ - WARNING -    - Resources端点: http://0.0.0.0:8011/mcp/resources
2025-07-08 23:11:54 - __main__ - WARNING - 📊 主要功能模块:
2025-07-08 23:11:54 - __main__ - WARNING -    1. 🔍 名称查询 - 人员/公司/部门精确搜索
2025-07-08 23:11:54 - __main__ - WARNING -    2. 🗄️ 数据导出 - Job/Booking数据Excel/CSV导出
2025-07-08 23:11:54 - __main__ - WARNING -    3. 🤖 AI数据分析 - 基于PandasAI的自然语言数据分析
2025-07-08 23:11:54 - __main__ - WARNING -    4. 📊 系统监控 - 健康检查和状态监控
2025-07-08 23:11:54 - __main__ - WARNING - 🔒 安全特性:
2025-07-08 23:11:54 - __main__ - WARNING -    - 访问控制: 严格的权限验证和访问控制
2025-07-08 23:11:54 - __main__ - WARNING -    - 数据安全: 安全的数据查询和导出
2025-07-08 23:11:54 - __main__ - WARNING -    - 超时保护: 导出超时保护和错误处理
2025-07-08 23:11:54 - __main__ - WARNING -    - 认证: MCP Token验证已启用
2025-07-08 23:11:54 - __main__ - WARNING - ⚙️ 配置: 单worker模式, 专注数据导出, 并发限制: 100
2025-07-08 23:11:54 - __main__ - WARNING - 🔧 网络: 连接保持: 600s, 队列: 256
2025-07-08 23:11:54 - __main__ - WARNING - ============================================================
2025-07-08 23:11:54 - __main__ - WARNING - 正在初始化MCP服务组件...
2025-07-08 23:11:56 - __main__ - WARNING - MCP服务初始化完成，开始接受连接
2025-07-08 23:11:56 - __main__ - WARNING - ⏸️ 自动提取功能已禁用
2025-07-08 23:11:56 - __main__ - WARNING - 💡 如需启用往期job和booking业务数据自动提取功能，请使用 --enable-auto-extract 参数
2025-07-08 23:11:56 - __main__ - WARNING - 开始启动CMS企业管理系统服务器...
2025-07-08 23:14:44 - __main__ - WARNING - 收到信号 2，开始优雅关闭...
2025-07-08 23:20:10 - __main__ - WARNING - 系统信息: macOS-15.5-arm64-arm-64bit, Python: 3.12.9
2025-07-08 23:20:10 - __main__ - WARNING - CPU核心数: 11, 配置的Worker数: 8
2025-07-08 23:20:10 - __main__ - WARNING - 主机名: lixiaojuns-MacBook-Pro.local, IP: 127.0.0.1
2025-07-08 23:20:10 - __main__ - WARNING - 服务监听: 0.0.0.0:8011, 基础URL: http://127.0.0.1:8011
2025-07-08 23:20:10 - __main__ - WARNING - 检测到本地开发环境，使用标准配置
2025-07-08 23:20:10 - __main__ - WARNING - 已完成优化版MCP服务挂载:
2025-07-08 23:20:10 - __main__ - WARNING -   - /mcp: CMS企业管理系统 (全功能优化版)
2025-07-08 23:20:10 - __main__ - WARNING -     * MCP SSE端点: /mcp/sse
2025-07-08 23:20:10 - __main__ - WARNING -     * MCP Tools端点: /mcp/tools
2025-07-08 23:20:10 - __main__ - WARNING -     * MCP Resources端点: /mcp/resources
2025-07-08 23:20:10 - __main__ - WARNING -     * 主要功能: 搜索、数据导出、PandasAI分析、系统监控
2025-07-08 23:20:10 - __main__ - WARNING - Prometheus监控服务启动在 :8111
2025-07-08 23:20:10 - __main__ - WARNING - === CMS企业管理系统MCP服务器启动信息 ===
2025-07-08 23:20:10 - __main__ - WARNING - 🚀 启动时间: 2025-07-08 23:20:10
2025-07-08 23:20:10 - __main__ - WARNING - 🌐 服务地址: http://0.0.0.0:8011
2025-07-08 23:20:10 - __main__ - WARNING - 🔥 精简版特色: 专注数据导出，高效可靠，支持名称查询
2025-07-08 23:20:10 - __main__ - WARNING - 📊 MCP连接信息:
2025-07-08 23:20:10 - __main__ - WARNING -    - SSE连接地址: http://0.0.0.0:8011/mcp/sse
2025-07-08 23:20:10 - __main__ - WARNING -    - Tools端点: http://0.0.0.0:8011/mcp/tools
2025-07-08 23:20:10 - __main__ - WARNING -    - Resources端点: http://0.0.0.0:8011/mcp/resources
2025-07-08 23:20:10 - __main__ - WARNING - 📊 主要功能模块:
2025-07-08 23:20:10 - __main__ - WARNING -    1. 🔍 名称查询 - 人员/公司/部门精确搜索
2025-07-08 23:20:10 - __main__ - WARNING -    2. 🗄️ 数据导出 - Job/Booking数据Excel/CSV导出
2025-07-08 23:20:10 - __main__ - WARNING -    3. 🤖 AI数据分析 - 基于PandasAI的自然语言数据分析
2025-07-08 23:20:10 - __main__ - WARNING -    4. 📊 系统监控 - 健康检查和状态监控
2025-07-08 23:20:10 - __main__ - WARNING - 🔒 安全特性:
2025-07-08 23:20:10 - __main__ - WARNING -    - 访问控制: 严格的权限验证和访问控制
2025-07-08 23:20:10 - __main__ - WARNING -    - 数据安全: 安全的数据查询和导出
2025-07-08 23:20:10 - __main__ - WARNING -    - 超时保护: 导出超时保护和错误处理
2025-07-08 23:20:10 - __main__ - WARNING -    - 认证: MCP Token验证已启用
2025-07-08 23:20:10 - __main__ - WARNING - ⚙️ 配置: 单worker模式, 专注数据导出, 并发限制: 100
2025-07-08 23:20:10 - __main__ - WARNING - 🔧 网络: 连接保持: 600s, 队列: 256
2025-07-08 23:20:10 - __main__ - WARNING - ============================================================
2025-07-08 23:20:10 - __main__ - WARNING - 正在初始化MCP服务组件...
2025-07-08 23:20:12 - __main__ - WARNING - MCP服务初始化完成，开始接受连接
2025-07-08 23:20:12 - __main__ - WARNING - ⏸️ 自动提取功能已禁用
2025-07-08 23:20:12 - __main__ - WARNING - 💡 如需启用往期job和booking业务数据自动提取功能，请使用 --enable-auto-extract 参数
2025-07-08 23:20:12 - __main__ - WARNING - 开始启动CMS企业管理系统服务器...
2025-07-08 23:41:41 - __main__ - WARNING - 收到信号 2，开始优雅关闭...
2025-07-08 23:42:18 - __main__ - WARNING - 系统信息: macOS-15.5-arm64-arm-64bit, Python: 3.12.9
2025-07-08 23:42:18 - __main__ - WARNING - CPU核心数: 11, 配置的Worker数: 8
2025-07-08 23:42:18 - __main__ - WARNING - 主机名: lixiaojuns-MacBook-Pro.local, IP: 127.0.0.1
2025-07-08 23:42:18 - __main__ - WARNING - 服务监听: 0.0.0.0:8011, 基础URL: http://127.0.0.1:8011
2025-07-08 23:42:18 - __main__ - WARNING - 检测到本地开发环境，使用标准配置
2025-07-08 23:42:18 - __main__ - WARNING - 已完成优化版MCP服务挂载:
2025-07-08 23:42:18 - __main__ - WARNING -   - /mcp: CMS企业管理系统 (全功能优化版)
2025-07-08 23:42:18 - __main__ - WARNING -     * MCP SSE端点: /mcp/sse
2025-07-08 23:42:18 - __main__ - WARNING -     * MCP Tools端点: /mcp/tools
2025-07-08 23:42:18 - __main__ - WARNING -     * MCP Resources端点: /mcp/resources
2025-07-08 23:42:18 - __main__ - WARNING -     * 主要功能: 搜索、数据导出、PandasAI分析、系统监控
2025-07-08 23:42:18 - __main__ - WARNING - Prometheus监控服务启动在 :8111
2025-07-08 23:42:18 - __main__ - WARNING - === CMS企业管理系统MCP服务器启动信息 ===
2025-07-08 23:42:18 - __main__ - WARNING - 🚀 启动时间: 2025-07-08 23:42:18
2025-07-08 23:42:18 - __main__ - WARNING - 🌐 服务地址: http://0.0.0.0:8011
2025-07-08 23:42:18 - __main__ - WARNING - 🔥 精简版特色: 专注数据导出，高效可靠，支持名称查询
2025-07-08 23:42:18 - __main__ - WARNING - 📊 MCP连接信息:
2025-07-08 23:42:18 - __main__ - WARNING -    - SSE连接地址: http://0.0.0.0:8011/mcp/sse
2025-07-08 23:42:18 - __main__ - WARNING -    - Tools端点: http://0.0.0.0:8011/mcp/tools
2025-07-08 23:42:18 - __main__ - WARNING -    - Resources端点: http://0.0.0.0:8011/mcp/resources
2025-07-08 23:42:18 - __main__ - WARNING - 📊 主要功能模块:
2025-07-08 23:42:18 - __main__ - WARNING -    1. 🔍 名称查询 - 人员/公司/部门精确搜索
2025-07-08 23:42:18 - __main__ - WARNING -    2. 🗄️ 数据导出 - Job/Booking数据Excel/CSV导出
2025-07-08 23:42:18 - __main__ - WARNING -    3. 🤖 AI数据分析 - 基于PandasAI的自然语言数据分析
2025-07-08 23:42:18 - __main__ - WARNING -    4. 📊 系统监控 - 健康检查和状态监控
2025-07-08 23:42:18 - __main__ - WARNING - 🔒 安全特性:
2025-07-08 23:42:18 - __main__ - WARNING -    - 访问控制: 严格的权限验证和访问控制
2025-07-08 23:42:18 - __main__ - WARNING -    - 数据安全: 安全的数据查询和导出
2025-07-08 23:42:18 - __main__ - WARNING -    - 超时保护: 导出超时保护和错误处理
2025-07-08 23:42:18 - __main__ - WARNING -    - 认证: MCP Token验证已启用
2025-07-08 23:42:18 - __main__ - WARNING - ⚙️ 配置: 单worker模式, 专注数据导出, 并发限制: 100
2025-07-08 23:42:18 - __main__ - WARNING - 🔧 网络: 连接保持: 600s, 队列: 256
2025-07-08 23:42:18 - __main__ - WARNING - ============================================================
2025-07-08 23:42:18 - __main__ - WARNING - 正在初始化MCP服务组件...
2025-07-08 23:42:20 - __main__ - WARNING - MCP服务初始化完成，开始接受连接
2025-07-08 23:42:20 - __main__ - WARNING - ⏸️ 自动提取功能已禁用
2025-07-08 23:42:20 - __main__ - WARNING - 💡 如需启用往期job和booking业务数据自动提取功能，请使用 --enable-auto-extract 参数
2025-07-08 23:42:20 - __main__ - WARNING - 开始启动CMS企业管理系统服务器...
2025-07-08 23:51:27 - __main__ - WARNING - 收到信号 2，开始优雅关闭...
2025-07-09 00:01:40 - __main__ - WARNING - 系统信息: macOS-15.5-arm64-arm-64bit, Python: 3.12.9
2025-07-09 00:01:40 - __main__ - WARNING - CPU核心数: 11, 配置的Worker数: 8
2025-07-09 00:01:40 - __main__ - WARNING - 主机名: lixiaojuns-MacBook-Pro.local, IP: 127.0.0.1
2025-07-09 00:01:40 - __main__ - WARNING - 服务监听: 0.0.0.0:8011, 基础URL: http://127.0.0.1:8011
2025-07-09 00:01:40 - __main__ - WARNING - 检测到本地开发环境，使用标准配置
2025-07-09 00:01:40 - __main__ - WARNING - 已完成优化版MCP服务挂载:
2025-07-09 00:01:40 - __main__ - WARNING -   - /mcp: CMS企业管理系统 (全功能优化版)
2025-07-09 00:01:40 - __main__ - WARNING -     * MCP SSE端点: /mcp/sse
2025-07-09 00:01:40 - __main__ - WARNING -     * MCP Tools端点: /mcp/tools
2025-07-09 00:01:40 - __main__ - WARNING -     * MCP Resources端点: /mcp/resources
2025-07-09 00:01:40 - __main__ - WARNING -     * 主要功能: 搜索、数据导出、PandasAI分析、系统监控
2025-07-09 00:01:40 - __main__ - WARNING - Prometheus监控服务启动在 :8111
2025-07-09 00:01:40 - __main__ - WARNING - === CMS企业管理系统MCP服务器启动信息 ===
2025-07-09 00:01:40 - __main__ - WARNING - 🚀 启动时间: 2025-07-09 00:01:40
2025-07-09 00:01:40 - __main__ - WARNING - 🌐 服务地址: http://0.0.0.0:8011
2025-07-09 00:01:40 - __main__ - WARNING - 🔥 精简版特色: 专注数据导出，高效可靠，支持名称查询
2025-07-09 00:01:40 - __main__ - WARNING - 📊 MCP连接信息:
2025-07-09 00:01:40 - __main__ - WARNING -    - SSE连接地址: http://0.0.0.0:8011/mcp/sse
2025-07-09 00:01:40 - __main__ - WARNING -    - Tools端点: http://0.0.0.0:8011/mcp/tools
2025-07-09 00:01:40 - __main__ - WARNING -    - Resources端点: http://0.0.0.0:8011/mcp/resources
2025-07-09 00:01:40 - __main__ - WARNING - 📊 主要功能模块:
2025-07-09 00:01:40 - __main__ - WARNING -    1. 🔍 名称查询 - 人员/公司/部门精确搜索
2025-07-09 00:01:40 - __main__ - WARNING -    2. 🗄️ 数据导出 - Job/Booking数据Excel/CSV导出
2025-07-09 00:01:40 - __main__ - WARNING -    3. 🤖 AI数据分析 - 基于PandasAI的自然语言数据分析
2025-07-09 00:01:40 - __main__ - WARNING -    4. 📊 系统监控 - 健康检查和状态监控
2025-07-09 00:01:40 - __main__ - WARNING - 🔒 安全特性:
2025-07-09 00:01:40 - __main__ - WARNING -    - 访问控制: 严格的权限验证和访问控制
2025-07-09 00:01:40 - __main__ - WARNING -    - 数据安全: 安全的数据查询和导出
2025-07-09 00:01:40 - __main__ - WARNING -    - 超时保护: 导出超时保护和错误处理
2025-07-09 00:01:40 - __main__ - WARNING -    - 认证: MCP Token验证已启用
2025-07-09 00:01:40 - __main__ - WARNING - ⚙️ 配置: 单worker模式, 专注数据导出, 并发限制: 100
2025-07-09 00:01:40 - __main__ - WARNING - 🔧 网络: 连接保持: 600s, 队列: 256
2025-07-09 00:01:40 - __main__ - WARNING - ============================================================
2025-07-09 00:01:40 - __main__ - WARNING - 正在初始化MCP服务组件...
2025-07-09 00:01:42 - __main__ - WARNING - MCP服务初始化完成，开始接受连接
2025-07-09 00:01:42 - __main__ - WARNING - ⏸️ 自动提取功能已禁用
2025-07-09 00:01:42 - __main__ - WARNING - 💡 如需启用往期job和booking业务数据自动提取功能，请使用 --enable-auto-extract 参数
2025-07-09 00:01:42 - __main__ - WARNING - 开始启动CMS企业管理系统服务器...
