2025-07-08 23:20:10 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-08 23:21:56 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_with_transhipment_2025-06-01_2025-06-30
2025-07-08 23:21:56 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_with_transhipment 获取数据
2025-07-08 23:22:13 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_with_transhipment_2025-06-01_2025-06-30, 记录数: 535
2025-07-08 23:22:13 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_with_transhipment 执行完成，耗时: 17.81 秒
2025-07-08 23:24:04 - utils.basic.data_cache_manager - INFO - 缓存命中: get_sea_air_profit_with_transhipment_2025-06-01_2025-06-30
2025-07-08 23:42:18 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-08 23:43:41 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_sea_air_profit_with_transhipment_2025-06-01_2025-06-30
2025-07-08 23:43:41 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_sea_air_profit_with_transhipment 获取数据
2025-07-08 23:43:59 - utils.basic.data_cache_manager - INFO - 缓存数据: get_sea_air_profit_with_transhipment_2025-06-01_2025-06-30, 记录数: 535
2025-07-08 23:43:59 - utils.basic.data_cache_manager - INFO - 异步函数 get_sea_air_profit_with_transhipment 执行完成，耗时: 18.22 秒
2025-07-08 23:46:41 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_job_details_with_transhipment_2025-06-01_2025-06-30
2025-07-08 23:46:41 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_job_details_with_transhipment 获取数据
2025-07-08 23:49:11 - utils.basic.data_cache_manager - INFO - 缓存数据: get_job_details_with_transhipment_2025-06-01_2025-06-30, 记录数: 148
2025-07-08 23:49:11 - utils.basic.data_cache_manager - INFO - 异步函数 get_job_details_with_transhipment 执行完成，耗时: 150.08 秒
2025-07-08 23:49:47 - utils.basic.data_cache_manager - INFO - AI分析缓存未命中: ai_analysis_job_2025-06-01_2025-06-30
2025-07-08 23:49:47 - utils.basic.data_cache_manager - INFO - 缓存命中: get_job_details_with_transhipment_2025-06-01_2025-06-30
2025-07-08 23:49:47 - utils.basic.data_cache_manager - INFO - AI分析缓存数据: ai_analysis_job_2025-06-01_2025-06-30, 记录数: 148
2025-07-09 00:01:40 - utils.basic.data_cache_manager - INFO - 全局数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=100条
2025-07-09 00:01:57 - utils.basic.data_cache_manager - INFO - 清空了所有缓存: 0 个条目
2025-07-09 00:01:57 - utils.basic.data_cache_manager - INFO - AI分析缓存未命中: ai_analysis_job_2025-07-02_2025-07-09
2025-07-09 00:01:57 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_job_details_with_transhipment_2025-07-02_2025-07-09
2025-07-09 00:01:57 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_job_details_with_transhipment 获取数据
2025-07-09 00:02:58 - utils.basic.data_cache_manager - INFO - 缓存数据: get_job_details_with_transhipment_2025-07-02_2025-07-09, 记录数: 38
2025-07-09 00:02:58 - utils.basic.data_cache_manager - INFO - 异步函数 get_job_details_with_transhipment 执行完成，耗时: 60.66 秒
2025-07-09 00:02:58 - utils.basic.data_cache_manager - INFO - AI分析缓存数据: ai_analysis_job_2025-07-02_2025-07-09, 记录数: 38
2025-07-09 00:03:12 - utils.basic.data_cache_manager - INFO - AI分析缓存命中: ai_analysis_job_2025-07-02_2025-07-09
2025-07-09 00:04:49 - utils.basic.data_cache_manager - INFO - 清空了所有缓存: 2 个条目
2025-07-09 00:04:49 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_job_details_with_transhipment_2025-07-02_2025-07-09
2025-07-09 00:04:49 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_job_details_with_transhipment 获取数据
2025-07-09 00:05:43 - utils.basic.data_cache_manager - INFO - 缓存数据: get_job_details_with_transhipment_2025-07-02_2025-07-09, 记录数: 38
2025-07-09 00:05:43 - utils.basic.data_cache_manager - INFO - 异步函数 get_job_details_with_transhipment 执行完成，耗时: 53.16 秒
2025-07-09 00:05:43 - utils.basic.data_cache_manager - INFO - AI分析缓存未命中: ai_analysis_job_2025-07-02_2025-07-09
2025-07-09 00:05:43 - utils.basic.data_cache_manager - INFO - 缓存命中: get_job_details_with_transhipment_2025-07-02_2025-07-09
2025-07-09 00:05:43 - utils.basic.data_cache_manager - INFO - AI分析缓存数据: ai_analysis_job_2025-07-02_2025-07-09, 记录数: 38
2025-07-09 00:07:41 - utils.basic.data_cache_manager - INFO - AI分析缓存命中: ai_analysis_job_2025-07-02_2025-07-09
2025-07-09 00:09:45 - utils.basic.data_cache_manager - INFO - 缓存命中: get_job_details_with_transhipment_2025-07-02_2025-07-09
2025-07-09 00:09:58 - utils.basic.data_cache_manager - INFO - AI分析缓存命中: ai_analysis_job_2025-07-02_2025-07-09
2025-07-09 00:18:21 - utils.basic.data_cache_manager - INFO - 缓存未命中: get_job_details_with_transhipment_2023-07-02_2023-07-09
2025-07-09 00:18:21 - utils.basic.data_cache_manager - INFO - 执行异步函数 get_job_details_with_transhipment 获取数据
2025-07-09 00:18:59 - utils.basic.data_cache_manager - INFO - 缓存数据: get_job_details_with_transhipment_2023-07-02_2023-07-09, 记录数: 27
2025-07-09 00:18:59 - utils.basic.data_cache_manager - INFO - 异步函数 get_job_details_with_transhipment 执行完成，耗时: 38.78 秒
2025-07-09 00:19:59 - utils.basic.data_cache_manager - INFO - AI分析缓存未命中: ai_analysis_job_2023-07-02_2023-07-09
2025-07-09 00:19:59 - utils.basic.data_cache_manager - INFO - 缓存命中: get_job_details_with_transhipment_2023-07-02_2023-07-09
2025-07-09 00:19:59 - utils.basic.data_cache_manager - INFO - AI分析缓存数据: ai_analysis_job_2023-07-02_2023-07-09, 记录数: 27
