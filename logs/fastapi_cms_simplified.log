2025-07-10 23:10:36 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:10:57 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDatalake，包含t_booking_details和t_job_details表
2025-07-10 23:10:57 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析最近一个月的业务数据概况，包括总收入、总成本、利润率等关键指标
2025-07-10 23:10:57 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:11:23 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:11:39 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDatalake，包含t_booking_details和t_job_details表
2025-07-10 23:11:39 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析最近一个月的业务数据概况，包括总收入、总成本、利润率等关键指标
2025-07-10 23:11:39 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:12:16 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:12:48 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDatalake，包含t_booking_details和t_job_details表
2025-07-10 23:12:48 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析最近一个月的业务数据概况，包括总收入、总成本、利润率等关键指标
2025-07-10 23:12:48 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:16:27 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:17:00 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDatalake，包含t_booking_details和t_job_details表
2025-07-10 23:17:00 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析最近一个月的业务数据概况，包括总收入、总成本、利润率等关键指标
2025-07-10 23:17:00 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:21:04 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:21:20 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:21:20 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析最近一个月的业务数据概况，包括总收入、总成本、利润率等关键指标
2025-07-10 23:21:20 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:22:15 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:22:15 - utils.fastapi_apps.fastapi_cms_simplified - ERROR - PandasAI 分析失败: 'Engine' object has no attribute 'cursor'
2025-07-10 23:22:15 - utils.fastapi_apps.fastapi_cms_simplified - ERROR - 错误堆栈: Traceback (most recent call last):
  File "/Volumes/PSSD/code_files/mcp-cms/utils/fastapi_apps/fastapi_cms_simplified.py", line 153, in analyze_data_with_pandasai
    data_df = pd.read_sql(sql_query, engine)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/PSSD/code_files/mcp-cms/.venv/lib/python3.12/site-packages/pandas/io/sql.py", line 708, in read_sql
    return pandas_sql.read_query(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/PSSD/code_files/mcp-cms/.venv/lib/python3.12/site-packages/pandas/io/sql.py", line 2728, in read_query
    cursor = self.execute(sql, params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/PSSD/code_files/mcp-cms/.venv/lib/python3.12/site-packages/pandas/io/sql.py", line 2662, in execute
    cur = self.con.cursor()
          ^^^^^^^^^^^^^^^
AttributeError: 'Engine' object has no attribute 'cursor'

2025-07-10 23:22:39 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:22:55 - utils.fastapi_apps.fastapi_cms_simplified - ERROR - PandasAI 分析失败: 'Connection' object has no attribute 'cursor'
2025-07-10 23:22:55 - utils.fastapi_apps.fastapi_cms_simplified - ERROR - 错误堆栈: Traceback (most recent call last):
  File "/Volumes/PSSD/code_files/mcp-cms/utils/fastapi_apps/fastapi_cms_simplified.py", line 154, in analyze_data_with_pandasai
    data_df = pd.read_sql(sql_query, connection)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/PSSD/code_files/mcp-cms/.venv/lib/python3.12/site-packages/pandas/io/sql.py", line 708, in read_sql
    return pandas_sql.read_query(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/PSSD/code_files/mcp-cms/.venv/lib/python3.12/site-packages/pandas/io/sql.py", line 2728, in read_query
    cursor = self.execute(sql, params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Volumes/PSSD/code_files/mcp-cms/.venv/lib/python3.12/site-packages/pandas/io/sql.py", line 2662, in execute
    cur = self.con.cursor()
          ^^^^^^^^^^^^^^^
AttributeError: 'Connection' object has no attribute 'cursor'

2025-07-10 23:23:26 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:23:26 - utils.fastapi_apps.fastapi_cms_simplified - ERROR - PandasAI 分析失败: cannot import name 'get_db_connection_mcp' from 'utils.basic.data_conn_unified' (/Volumes/PSSD/code_files/mcp-cms/utils/basic/data_conn_unified.py)
2025-07-10 23:23:26 - utils.fastapi_apps.fastapi_cms_simplified - ERROR - 错误堆栈: Traceback (most recent call last):
  File "/Volumes/PSSD/code_files/mcp-cms/utils/fastapi_apps/fastapi_cms_simplified.py", line 140, in analyze_data_with_pandasai
    from utils.basic.data_conn_unified import get_db_connection_mcp
ImportError: cannot import name 'get_db_connection_mcp' from 'utils.basic.data_conn_unified' (/Volumes/PSSD/code_files/mcp-cms/utils/basic/data_conn_unified.py)

2025-07-10 23:24:32 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:24:50 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了4464条记录
2025-07-10 23:24:50 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:24:50 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析最近一个月的业务数据概况，包括总收入、总成本、利润率等关键指标
2025-07-10 23:25:16 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:25:40 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:25:59 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了4464条记录
2025-07-10 23:25:59 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:25:59 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析最近一个月的业务数据概况，包括总收入、总成本、利润率等关键指标
2025-07-10 23:26:24 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:26:52 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:27:10 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了4464条记录
2025-07-10 23:27:10 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:27:10 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 数据总共有多少条记录？
2025-07-10 23:27:16 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:27:43 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:28:01 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了4464条记录
2025-07-10 23:28:01 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:28:01 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析收入和成本的分布情况，计算平均利润率
2025-07-10 23:28:11 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:47:25 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:47:41 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了0条记录
2025-07-10 23:47:41 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:47:41 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析收入和成本的分布情况，计算平均利润率
2025-07-10 23:47:50 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:49:02 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:49:20 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了4464条记录
2025-07-10 23:49:20 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:49:20 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 数据库中有哪些不同的pro2_system_id值？每个值有多少条记录？
2025-07-10 23:49:48 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:52:04 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:52:21 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了878条记录
2025-07-10 23:52:21 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:52:21 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 数据总共有多少条记录？显示前5行数据的pro2_system_id字段值
2025-07-10 23:52:27 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:53:00 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:53:17 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了878条记录
2025-07-10 23:53:17 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:53:17 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析青岛公司最近一个月的业务数据，统计总收入、总成本和利润率
2025-07-10 23:53:24 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-10 23:58:34 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-10 23:58:51 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了878条记录
2025-07-10 23:58:51 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-10 23:58:51 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析青岛公司最近一个月的业务数据，统计总收入、总成本和利润率
2025-07-10 23:59:20 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:02:24 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:02:40 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:02:41 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:02:41 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 统计数据总记录数，并计算总收入和总成本
2025-07-11 00:02:46 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:03:22 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:03:38 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:03:38 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:03:38 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 数据总共有多少条记录？
2025-07-11 00:03:43 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:03:43 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:03:59 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:03:59 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:03:59 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 统计不同起运港的业务数量，并按照港口约定处理空值
2025-07-11 00:04:07 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:04:44 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:05:00 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:05:00 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:05:00 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 数据总共有多少条记录？
2025-07-11 00:05:02 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:05:02 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:05:18 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:05:18 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:05:18 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 统计不同起运港的业务数量，并按照港口约定处理空值
2025-07-11 00:05:23 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:06:03 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:06:19 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:06:19 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:06:19 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 显示pol_code和pod_code字段的前10条记录，包括这些字段的值
2025-07-11 00:06:22 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:06:22 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:06:22 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:06:22 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:06:22 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 统计pol_code字段的不同值及其出现次数，显示前10个最常见的港口
2025-07-11 00:06:25 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:06:25 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:06:26 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:06:26 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:06:26 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 
        根据港口约定规则分析起运港数据：
        1. 优先使用pol_code（提单起运地）
        2. 如果pol_code为空，则使用sailing_pol（航次始发港）
        3. 统计处理后的起运港分布情况
        
2025-07-11 00:06:30 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:07:03 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:07:19 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:07:19 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:07:19 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 显示数据表的所有列名，并显示前3条记录的所有字段值
2025-07-11 00:07:26 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:07:26 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:07:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:07:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:07:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 列出数据表的所有列名，并统计每列的非空值数量
2025-07-11 00:07:44 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:10:59 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:11:15 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:11:15 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:11:15 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 显示job_pol、bill_pol和bill_pod字段的前10条记录，包括这些字段的值
2025-07-11 00:11:18 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:11:18 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:11:34 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:11:34 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:11:34 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 统计bill_pol字段的不同值及其出现次数，显示前10个最常见的港口
2025-07-11 00:11:37 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:11:37 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:11:37 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:11:37 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:11:37 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 
        根据港口约定规则分析起运港数据：
        1. 优先使用bill_pol（提单起运港）
        2. 如果bill_pol为空，则使用job_pol（航次始发港）
        3. 统计处理后的起运港分布情况
        
2025-07-11 00:11:40 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:12:17 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:12:33 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:12:33 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:12:33 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 统计每个字段的非空值数量，只显示非空值数量大于0的字段
2025-07-11 00:12:36 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:12:36 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:12:52 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:12:52 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:12:52 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 显示前5条记录的所有字段，包括空值，以表格形式展示
2025-07-11 00:12:54 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:12:54 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:13:10 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:13:10 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:13:10 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 统计总收入、总成本、总利润，并显示有收入数据的记录数量
2025-07-11 00:13:14 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:20:31 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:20:47 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:20:47 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:20:47 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 根据表结构说明，列出当前数据表的所有主要字段，并说明每个字段的用途
2025-07-11 00:20:53 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:20:53 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:21:09 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:21:09 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:21:09 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 根据港口约定规则，说明在分析起运港和目的港时应该如何处理job_pol、bill_pol和bill_pod字段
2025-07-11 00:21:13 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:21:13 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:21:30 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_booking_details表读取了852条记录
2025-07-11 00:21:30 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_booking_details表
2025-07-11 00:21:30 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 当前系统所在地是哪里？在进行进口业务分析时，如果目的港为空应该如何处理？
2025-07-11 00:21:34 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-11 00:21:34 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 连接到MySQL数据库: qd.cmsgroup.com.cn:33306/mcp_tokens
2025-07-11 00:21:34 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 从t_job_details表读取了385条记录
2025-07-11 00:21:34 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 成功创建SmartDataframe，连接到t_job_details表
2025-07-11 00:21:34 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 根据Job数据的表结构，说明pol_code和pod_code字段的作用，以及在港口分析中的处理规则
2025-07-11 00:21:40 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
