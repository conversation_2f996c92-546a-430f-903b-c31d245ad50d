2025-07-07 21:54:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 正在启动利润数据周期性调度器...
2025-07-07 21:54:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO - ✅ 利润数据调度器已启动（后台运行）
2025-07-07 21:54:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 📋 调度规则：
2025-07-07 21:54:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO -    • 距今2年以上：每3个月检查一次
2025-07-07 21:54:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO -    • 距今1-2年：每3个月检查一次
2025-07-07 21:54:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO -    • 距今3个月-1年：每1个月检查一次
2025-07-07 21:54:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO -    • 距今1-3个月：每天检查一次
2025-07-07 21:54:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO -    • 3个月数据周期，自动增量更新
2025-07-07 21:54:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO -    • 执行时间：每日20:00至次日8:00
2025-07-07 22:00:14 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 正在关闭利润数据调度器...
2025-07-07 22:00:14 - utils.fastapi_apps.fastapi_cms_simplified - INFO - ✅ 利润数据调度器已关闭
2025-07-07 22:29:14 - utils.fastapi_apps.fastapi_cms_simplified - INFO - ⏸️ 自动提取功能已禁用
2025-07-07 22:29:14 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 💡 往期job和booking业务数据自动提取功能未启用
2025-07-07 22:29:14 - utils.fastapi_apps.fastapi_cms_simplified - INFO -    如需启用，请使用 --enable-auto-extract 参数重启服务器
2025-07-07 22:33:23 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始导出Booking数据: 2025-04-01 至 2025-04-30
2025-07-07 22:40:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO - ⏸️ 自动提取功能已禁用
2025-07-07 22:40:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 💡 往期job和booking业务数据自动提取功能未启用
2025-07-07 22:40:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO -    如需启用，请使用 --enable-auto-extract 参数重启服务器
2025-07-07 22:41:15 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始导出Job数据: 2025-04-01 至 2025-04-30
2025-07-08 23:11:54 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 数据缓存管理器初始化: 过期时间=30分钟, 最大缓存=50条
2025-07-08 23:11:56 - utils.fastapi_apps.fastapi_cms_simplified - INFO - ⏸️ 自动提取功能已禁用
2025-07-08 23:11:56 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 💡 往期job和booking业务数据自动提取功能未启用
2025-07-08 23:11:56 - utils.fastapi_apps.fastapi_cms_simplified - INFO -    如需启用，请使用 --enable-auto-extract 参数重启服务器
2025-07-08 23:20:12 - utils.fastapi_apps.fastapi_cms_simplified - INFO - ⏸️ 自动提取功能已禁用
2025-07-08 23:20:12 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 💡 往期job和booking业务数据自动提取功能未启用
2025-07-08 23:20:12 - utils.fastapi_apps.fastapi_cms_simplified - INFO -    如需启用，请使用 --enable-auto-extract 参数重启服务器
2025-07-08 23:21:56 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始导出Booking数据: 2025-06-01 至 2025-06-30
2025-07-08 23:24:04 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始数据分析: 类型=booking, 日期=2025-06-01 至 2025-06-30, 问题=哪个handling agent的业务量（booking数量）最多？
2025-07-08 23:24:04 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 获取到 535 条booking数据，开始AI分析
2025-07-08 23:24:04 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 准备分析数据: 535 行, 43 列
2025-07-08 23:24:05 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 哪个handling agent的业务量（booking数量）最多？
2025-07-08 23:24:09 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-08 23:42:20 - utils.fastapi_apps.fastapi_cms_simplified - INFO - ⏸️ 自动提取功能已禁用
2025-07-08 23:42:20 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 💡 往期job和booking业务数据自动提取功能未启用
2025-07-08 23:42:20 - utils.fastapi_apps.fastapi_cms_simplified - INFO -    如需启用，请使用 --enable-auto-extract 参数重启服务器
2025-07-08 23:43:41 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始导出Booking数据: 2025-06-01 至 2025-06-30
2025-07-08 23:45:40 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 统一搜索请求: 类型=company, 关键词=代理
2025-07-08 23:45:40 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 统一搜索请求: 类型=company, 关键词=代理
2025-07-08 23:46:41 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始导出Job数据: 2025-06-01 至 2025-06-30
2025-07-08 23:49:47 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始数据分析: 类型=job, 日期=2025-06-01 至 2025-06-30, 问题=请找出前三个业务量最多的代理，提供每个代理的票数、rt、收入和毛利润数据。
2025-07-08 23:49:47 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI分析缓存未命中，获取并转换job数据
2025-07-08 23:49:47 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 数据转换完成并已缓存，记录数: 148
2025-07-08 23:49:47 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 获取到 148 条job数据（中文列名格式），开始AI分析
2025-07-08 23:49:47 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 准备分析数据: 148 行, 28 列
2025-07-08 23:49:47 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 请找出前三个业务量最多的代理，提供每个代理的票数、rt、收入和毛利润数据。
2025-07-08 23:50:08 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-09 00:01:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO - ⏸️ 自动提取功能已禁用
2025-07-09 00:01:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 💡 往期job和booking业务数据自动提取功能未启用
2025-07-09 00:01:42 - utils.fastapi_apps.fastapi_cms_simplified - INFO -    如需启用，请使用 --enable-auto-extract 参数重启服务器
2025-07-09 00:01:57 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始数据分析: 类型=job, 日期=2025-07-02 至 2025-07-09, 问题=请分析工作档数据中的业务类型分布，统计各个业务类型的数量和占比
2025-07-09 00:01:57 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI分析缓存未命中，获取并转换job数据
2025-07-09 00:02:58 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 数据转换完成并已缓存，记录数: 38
2025-07-09 00:02:58 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 获取到 38 条job数据（中文列名格式），开始AI分析
2025-07-09 00:02:58 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 准备分析数据: 38 行, 28 列
2025-07-09 00:02:58 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 请分析工作档数据中的业务类型分布，统计各个业务类型的数量和占比
2025-07-09 00:03:12 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-09 00:03:12 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始数据分析: 类型=job, 日期=2025-07-02 至 2025-07-09, 问题=请分析操作员的工作效率，统计各个操作员处理的工作档数量
2025-07-09 00:03:12 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 使用AI分析缓存数据，跳过数据获取和转换
2025-07-09 00:03:12 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 获取到 38 条job数据（中文列名格式），开始AI分析
2025-07-09 00:03:12 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 准备分析数据: 38 行, 28 列
2025-07-09 00:03:12 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 请分析操作员的工作效率，统计各个操作员处理的工作档数量
2025-07-09 00:03:17 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-09 00:04:49 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始导出Job数据: 2025-07-02 至 2025-07-09
2025-07-09 00:05:43 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始数据分析: 类型=job, 日期=2025-07-02 至 2025-07-09, 问题=分析工作档数据的操作完成情况，统计各个状态的数量和比例
2025-07-09 00:05:43 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI分析缓存未命中，获取并转换job数据
2025-07-09 00:05:43 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 数据转换完成并已缓存，记录数: 38
2025-07-09 00:05:43 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 获取到 38 条job数据（中文列名格式），开始AI分析
2025-07-09 00:05:43 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 准备分析数据: 38 行, 28 列
2025-07-09 00:05:43 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 分析工作档数据的操作完成情况，统计各个状态的数量和比例
2025-07-09 00:05:47 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-09 00:07:41 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始数据分析: 类型=job, 日期=2025-07-02 至 2025-07-09, 问题=在代理列中，哪个代理的数量最多？
2025-07-09 00:07:41 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 使用AI分析缓存数据，跳过数据获取和转换
2025-07-09 00:07:41 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 获取到 38 条job数据（中文列名格式），开始AI分析
2025-07-09 00:07:41 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 准备分析数据: 38 行, 28 列
2025-07-09 00:07:41 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 在代理列中，哪个代理的数量最多？
2025-07-09 00:07:45 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-09 00:09:45 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始导出Job数据: 2025-07-02 至 2025-07-09
2025-07-09 00:09:58 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始数据分析: 类型=job, 日期=2025-07-02 至 2025-07-09, 问题=统计代理列中出现次数最多的代理名称
2025-07-09 00:09:58 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 使用AI分析缓存数据，跳过数据获取和转换
2025-07-09 00:09:58 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 获取到 38 条job数据（中文列名格式），开始AI分析
2025-07-09 00:09:58 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 准备分析数据: 38 行, 28 列
2025-07-09 00:09:58 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 统计代理列中出现次数最多的代理名称
2025-07-09 00:10:02 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
2025-07-09 00:18:21 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始导出Job数据: 2023-07-02 至 2023-07-09
2025-07-09 00:19:59 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始数据分析: 类型=job, 日期=2023-07-02 至 2023-07-09, 问题=按工作档代理分组，计算各代理的总毛利润，并找出毛利润最高的代理。
2025-07-09 00:19:59 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI分析缓存未命中，获取并转换job数据
2025-07-09 00:19:59 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 数据转换完成并已缓存，记录数: 27
2025-07-09 00:19:59 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 获取到 27 条job数据（中文列名格式），开始AI分析
2025-07-09 00:19:59 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 准备分析数据: 27 行, 28 列
2025-07-09 00:19:59 - utils.fastapi_apps.fastapi_cms_simplified - INFO - 开始执行 AI 分析: 按工作档代理分组，计算各代理的总毛利润，并找出毛利润最高的代理。
2025-07-09 00:20:04 - utils.fastapi_apps.fastapi_cms_simplified - INFO - AI 分析完成
