# Pro2数据库基本操作

import asyncio
from typing import Dict, Any
from dotenv import load_dotenv
from utils.basic.db_pro2_sea_air_profit import (
    get_booking_details_with_transhipment,
    query_job_details_with_statistics_by_date
)
from utils.basic.logger_config import setup_logger
from utils.basic.data_cache_manager import async_cached_data_function

load_dotenv(override=True)

# 配置日志
logger = setup_logger(
    name=__name__,
    level="warning",
    log_to_console=True,
    log_to_file=True
)




# 根据时间周期查询海运空运损益并添加转运利润字段 (调度器使用)
@async_cached_data_function
async def get_sea_air_profit_with_transhipment(begin_date: str, end_date: str) -> Dict[str, Any]:
    """
    根据时间周期查询海运空运损益并添加转运利润字段 (调度器使用)
    使用 query_business_details_by_date 函数直接获取包含转运功能的业务明细数据
    """
    logger.warning(f"开始查询海空损益数据（含转运）: {begin_date} 到 {end_date}")
    
    try:
        # 使用已存在的函数直接获取包含转运功能的业务数据
        results = await asyncio.to_thread(
            get_booking_details_with_transhipment,
            begin_date,
            end_date,
            logger_prefix="[Scheduler]"
        )
        
        logger.warning(f"海空损益数据查询完成（含转运）: {len(results)} 条记录")
        
        return {
            'data': results,
            'total_count': len(results),
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部订舱毛利数据（含转运）'
            }
        }
        
    except Exception as e:
        logger.error(f"查询海空损益数据失败（含转运）: {e}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return {
            'data': [],
            'total_count': 0,
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部订舱毛利数据（含转运）',
                'error': f'查询失败: {str(e)}'
            }
        }

# 根据时间周期查询作业明细并添加转运利润字段 (调度器使用)
@async_cached_data_function
async def get_job_details_with_transhipment(begin_date: str, end_date: str) -> Dict[str, Any]:
    """
    根据时间周期查询作业明细并添加转运利润字段 (调度器使用)
    使用 query_job_details_with_statistics_by_date 函数直接获取包含转运功能的作业明细数据
    """
    logger.warning(f"开始查询作业明细数据（含转运）: {begin_date} 到 {end_date}")
    
    try:
        # 使用已存在的函数直接获取包含转运功能的作业数据
        results = await asyncio.to_thread(
            query_job_details_with_statistics_by_date,
            begin_date,
            end_date,
            logger_prefix="[Scheduler]"
        )
        
        if not results:
            logger.warning(f"未查询到作业明细数据: {begin_date} 到 {end_date}")
            return {
                'data': [],
                'total_count': 0,
                'query_info': {
                    'date_range': f'{begin_date} 到 {end_date}',
                    'data_type': '全部作业明细数据（含转运）',
                    'message': '未查询到数据'
                }
            }
        
        logger.warning(f"作业明细数据查询完成（含转运）: {len(results)} 条记录")
        
        return {
            'data': results,
            'total_count': len(results),
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部作业明细数据（含转运）'
            }
        }
        
    except Exception as e:
        logger.error(f"查询作业明细数据失败（含转运）: {e}")
        import traceback
        logger.error(f"错误堆栈: {traceback.format_exc()}")
        return {
            'data': [],
            'total_count': 0,
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部作业明细数据（含转运）',
                'error': f'查询失败: {str(e)}'
            }
        }
